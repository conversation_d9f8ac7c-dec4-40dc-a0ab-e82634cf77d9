# 噪声地图生成系统使用说明

## 概述
这是一个基于Perlin噪声的随机地图生成系统，参考了Red Blob Games的技术方案。系统分为三个主要组件：
- `NoiseConfigSO.cs` - ScriptableObject配置文件
- `NoiseAlgorithm.cs` - 独立的噪声算法实现
- `NoiseMap.cs` - 主要的地图生成和显示脚本

## 快速开始

### 1. 创建噪声配置文件
1. 在Project窗口中右键点击
2. 选择 `Create > Map > Noise Config`
3. 命名配置文件（如"DefaultNoiseConfig"）

### 2. 设置场景
1. 创建一个空的GameObject
2. 添加`NoiseMap`组件
3. 创建一个Plane或Quad作为显示表面
4. 将Plane的Renderer拖拽到NoiseMap的`Texture Renderer`字段
5. 将创建的噪声配置文件拖拽到`Noise Config`字段

### 3. 生成地图
- 运行场景，地图会自动生成
- 或在编辑器中点击"生成地图"按钮

## 参数说明

### 基础设置
- **种子(Seed)**: 控制随机性，相同种子产生相同地图
- **地图宽度/高度**: 生成地图的像素尺寸

### 噪声参数
- **噪声缩放(Noise Scale)**: 控制地形特征大小，值越小特征越大
- **Octaves数量**: 噪声层数，影响地形细节丰富度
- **持续性(Persistence)**: 控制每层振幅衰减，影响地形粗糙度
- **间隙度(Lacunarity)**: 控制每层频率增长，影响地形复杂度

### 地形重分布
- **高度曲线(Height Curve)**: 重塑高度分布，创造平谷和陡峰

### 偏移设置
- **X/Y轴偏移**: 移动噪声采样位置，保持地形连续性

### 边缘平滑
- **启用边缘平滑**: 是否在地图边缘应用平滑过渡
- **平滑强度**: 边缘平滑的强度
- **平滑范围**: 从边缘向内的平滑距离比例

## 实时调整
- 启用配置文件中的"自动更新"选项
- 在编辑器中调整参数时地图会实时更新
- 可以通过"随机种子"按钮快速生成不同的地图

## 性能优化建议
1. 较大的地图尺寸会显著影响生成时间
2. 过多的Octaves会增加计算复杂度
3. 在运行时频繁更新大地图可能影响帧率
4. 建议在编辑器中调试参数，运行时使用固定配置

## 扩展功能
- `GetHeightAtPosition()`: 获取指定位置的高度值
- `GetHeightAtWorldPosition()`: 获取世界坐标对应的高度值
- `GetNoiseMapCopy()`: 获取噪声地图数据副本

## 故障排除
- 如果地图显示为纯色，检查噪声缩放参数是否过小
- 如果地图没有显示，确保Texture Renderer已正确设置
- 如果实时更新不工作，检查"自动更新"选项是否启用
