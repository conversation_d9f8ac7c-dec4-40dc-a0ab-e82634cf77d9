using UnityEngine;

public class NoiseMap : MonoBehaviour
{
    [Header("配置")]
    [Tooltip("噪声配置文件")]
    public NoiseConfigSO noiseConfig;

    [Header("显示设置")]
    [Tooltip("用于显示地图的渲染器")]
    public Renderer textureRenderer;

    [Tooltip("是否在Start时自动生成地图")]
    public bool generateOnStart = true;

    [Header("调试信息")]
    [Tooltip("显示生成时间")]
    public bool showGenerationTime = true;

    // 私有变量
    private float[,] currentNoiseMap;
    private Texture2D currentTexture;
    private NoiseConfigSO lastConfig;

    void Start()
    {
        if (generateOnStart && noiseConfig != null)
        {
            GenerateMap();
        }
    }

    void Update()
    {
        // 检查配置是否改变，如果启用了自动更新则重新生成
        if (noiseConfig != null && noiseConfig.autoUpdate && HasConfigChanged())
        {
            GenerateMap();
        }
    }

    // 生成地图的主要方法
    public void GenerateMap()
    {
        if (noiseConfig == null)
        {
            Debug.LogError("NoiseMap: 噪声配置文件未设置！");
            return;
        }

        System.Diagnostics.Stopwatch stopwatch = null;
        if (showGenerationTime)
        {
            stopwatch = System.Diagnostics.Stopwatch.StartNew();
        }

        // 生成噪声地图
        currentNoiseMap = NoiseAlgorithm.GenerateNoiseMap(noiseConfig);

        // 生成纹理并应用到渲染器
        if (textureRenderer != null)
        {
            UpdateTexture();
        }

        // 更新配置缓存
        CacheCurrentConfig();

        if (showGenerationTime && stopwatch != null)
        {
            stopwatch.Stop();
            Debug.Log($"地图生成完成，耗时: {stopwatch.ElapsedMilliseconds}ms");
        }
    }

    // 更新纹理显示
    private void UpdateTexture()
    {
        if (currentNoiseMap == null) return;

        // 销毁旧纹理以避免内存泄漏
        if (currentTexture != null)
        {
            if (Application.isPlaying)
            {
                Destroy(currentTexture);
            }
            else
            {
                DestroyImmediate(currentTexture);
            }
        }

        // 生成新纹理
        currentTexture = NoiseAlgorithm.GenerateTexture(currentNoiseMap, noiseConfig.mapWidth, noiseConfig.mapHeight);

        // 应用到材质
        if (textureRenderer.material != null)
        {
            textureRenderer.material.mainTexture = currentTexture;
        }
    }

    // 检查配置是否发生变化
    private bool HasConfigChanged()
    {
        if (lastConfig == null) return true;

        return lastConfig.seed != noiseConfig.seed ||
               lastConfig.mapWidth != noiseConfig.mapWidth ||
               lastConfig.mapHeight != noiseConfig.mapHeight ||
               !Mathf.Approximately(lastConfig.noiseScale, noiseConfig.noiseScale) ||
               lastConfig.octaves != noiseConfig.octaves ||
               !Mathf.Approximately(lastConfig.persistence, noiseConfig.persistence) ||
               !Mathf.Approximately(lastConfig.lacunarity, noiseConfig.lacunarity) ||
               !Mathf.Approximately(lastConfig.heightCurve, noiseConfig.heightCurve) ||
               !Mathf.Approximately(lastConfig.offsetX, noiseConfig.offsetX) ||
               !Mathf.Approximately(lastConfig.offsetY, noiseConfig.offsetY) ||
               lastConfig.enableEdgeSmoothing != noiseConfig.enableEdgeSmoothing ||
               !Mathf.Approximately(lastConfig.edgeSmoothingStrength, noiseConfig.edgeSmoothingStrength) ||
               !Mathf.Approximately(lastConfig.edgeSmoothingRange, noiseConfig.edgeSmoothingRange) ||
               !Mathf.Approximately(lastConfig.minHeight, noiseConfig.minHeight) ||
               !Mathf.Approximately(lastConfig.maxHeight, noiseConfig.maxHeight);
    }

    // 缓存当前配置
    private void CacheCurrentConfig()
    {
        if (lastConfig == null)
        {
            lastConfig = ScriptableObject.CreateInstance<NoiseConfigSO>();
        }

        lastConfig.seed = noiseConfig.seed;
        lastConfig.mapWidth = noiseConfig.mapWidth;
        lastConfig.mapHeight = noiseConfig.mapHeight;
        lastConfig.noiseScale = noiseConfig.noiseScale;
        lastConfig.octaves = noiseConfig.octaves;
        lastConfig.persistence = noiseConfig.persistence;
        lastConfig.lacunarity = noiseConfig.lacunarity;
        lastConfig.heightCurve = noiseConfig.heightCurve;
        lastConfig.offsetX = noiseConfig.offsetX;
        lastConfig.offsetY = noiseConfig.offsetY;
        lastConfig.enableEdgeSmoothing = noiseConfig.enableEdgeSmoothing;
        lastConfig.edgeSmoothingStrength = noiseConfig.edgeSmoothingStrength;
        lastConfig.edgeSmoothingRange = noiseConfig.edgeSmoothingRange;
        lastConfig.minHeight = noiseConfig.minHeight;
        lastConfig.maxHeight = noiseConfig.maxHeight;
    }

    // 获取指定位置的高度值
    public float GetHeightAtPosition(int x, int y)
    {
        if (currentNoiseMap == null) return 0f;
        if (x < 0 || x >= noiseConfig.mapWidth || y < 0 || y >= noiseConfig.mapHeight) return 0f;

        return currentNoiseMap[x, y];
    }

    // 获取指定位置的高度值（世界坐标）
    public float GetHeightAtWorldPosition(Vector3 worldPosition)
    {
        // 将世界坐标转换为地图坐标
        int x = Mathf.RoundToInt(worldPosition.x);
        int y = Mathf.RoundToInt(worldPosition.z);

        return GetHeightAtPosition(x, y);
    }

    // 获取当前噪声地图的副本
    public float[,] GetNoiseMapCopy()
    {
        if (currentNoiseMap == null) return null;

        float[,] copy = new float[noiseConfig.mapWidth, noiseConfig.mapHeight];
        System.Array.Copy(currentNoiseMap, copy, currentNoiseMap.Length);
        return copy;
    }

    // 手动触发地图生成（用于按钮等）
    [ContextMenu("生成地图")]
    public void GenerateMapManually()
    {
        GenerateMap();
    }

    // 随机生成新种子
    [ContextMenu("随机种子")]
    public void RandomizeSeed()
    {
        if (noiseConfig != null)
        {
            noiseConfig.seed = Random.Range(0, 999999);
            if (noiseConfig.autoUpdate)
            {
                GenerateMap();
            }
        }
    }

    // 清理资源
    void OnDestroy()
    {
        if (currentTexture != null)
        {
            if (Application.isPlaying)
            {
                Destroy(currentTexture);
            }
            else
            {
                DestroyImmediate(currentTexture);
            }
        }

        if (lastConfig != null)
        {
            if (Application.isPlaying)
            {
                Destroy(lastConfig);
            }
            else
            {
                DestroyImmediate(lastConfig);
            }
        }
    }

    // 编辑器中的验证
    void OnValidate()
    {
        if (noiseConfig != null && noiseConfig.autoUpdate && Application.isPlaying)
        {
            GenerateMap();
        }
    }
}