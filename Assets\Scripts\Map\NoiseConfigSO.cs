using UnityEngine;

[CreateAssetMenu(fileName = "NoiseConfig", menuName = "Map/Noise Config")]
public class NoiseConfigSO : ScriptableObject
{
    [Header("基础设置")]
    [Tooltip("噪声种子，相同种子产生相同地图")]
    public int seed = 12345;

    [Tooltip("地图宽度")]
    public int mapWidth = 256;

    [Tooltip("地图高度")]
    public int mapHeight = 256;

    [Header("噪声参数")]
    [Tooltip("噪声缩放，值越小地形特征越大")]
    [Range(0.001f, 0.1f)]
    public float noiseScale = 0.01f;

    [Tooltip("octaves数量，影响地形细节层次")]
    [Range(1, 6)]
    public int octaves = 4;

    [Tooltip("持续性，控制每层octave的振幅衰减")]
    [Range(0f, 1f)]
    public float persistence = 0.5f;

    [Tooltip("间隙度，控制每层octave的频率增长")]
    [Range(1f, 4f)]
    public float lacunarity = 2f;

    [Header("地形重分布")]
    [Tooltip("高度曲线指数，用于重塑地形分布")]
    [Range(0.1f, 5f)]
    public float heightCurve = 1.2f;

    [Header("偏移设置")]
    [Tooltip("X轴偏移")]
    public float offsetX = 0f;

    [Tooltip("Y轴偏移")]
    public float offsetY = 0f;

    [Header("边缘平滑")]
    [Tooltip("是否启用边缘平滑")]
    public bool enableEdgeSmoothing = true;

    [Tooltip("边缘平滑强度")]
    [Range(0f, 1f)]
    public float edgeSmoothingStrength = 0.5f;

    [Tooltip("边缘平滑范围（从边缘向内的距离比例）")]
    [Range(0.1f, 0.5f)]
    public float edgeSmoothingRange = 0.2f;

    [Header("高度范围")]
    [Tooltip("最小高度值")]
    [Range(0f, 1f)]
    public float minHeight = 0f;

    [Tooltip("最大高度值")]
    [Range(0f, 1f)]
    public float maxHeight = 1f;

    [Header("地形颜色配置")]
    [Tooltip("深海颜色")]
    public Color deepWaterColor = new Color(0.1f, 0.2f, 0.6f, 1f);

    [Tooltip("浅水颜色")]
    public Color shallowWaterColor = new Color(0.2f, 0.4f, 0.8f, 1f);

    [Tooltip("海滩颜色")]
    public Color beachColor = new Color(0.9f, 0.8f, 0.4f, 1f);

    [Tooltip("草地颜色")]
    public Color grassColor = new Color(0.3f, 0.7f, 0.2f, 1f);

    [Tooltip("森林颜色")]
    public Color forestColor = new Color(0.2f, 0.5f, 0.1f, 1f);

    [Tooltip("山地颜色")]
    public Color mountainColor = new Color(0.5f, 0.4f, 0.3f, 1f);

    [Tooltip("雪山颜色")]
    public Color snowColor = new Color(0.9f, 0.9f, 0.9f, 1f);

    [Header("高度阈值")]
    [Tooltip("深海到浅水的高度阈值")]
    [Range(0f, 1f)]
    public float deepWaterThreshold = 0.2f;

    [Tooltip("浅水到海滩的高度阈值")]
    [Range(0f, 1f)]
    public float shallowWaterThreshold = 0.3f;

    [Tooltip("海滩到草地的高度阈值")]
    [Range(0f, 1f)]
    public float beachThreshold = 0.4f;

    [Tooltip("草地到森林的高度阈值")]
    [Range(0f, 1f)]
    public float grassThreshold = 0.6f;

    [Tooltip("森林到山地的高度阈值")]
    [Range(0f, 1f)]
    public float forestThreshold = 0.8f;

    [Tooltip("山地到雪山的高度阈值")]
    [Range(0f, 1f)]
    public float mountainThreshold = 0.9f;

    [Header("实时更新")]
    [Tooltip("是否在编辑器中实时更新")]
    public bool autoUpdate = true;

    // 验证参数的合理性
    private void OnValidate()
    {
        if (mapWidth < 1) mapWidth = 1;
        if (mapHeight < 1) mapHeight = 1;
        if (octaves < 1) octaves = 1;
        if (noiseScale <= 0) noiseScale = 0.001f;
        if (minHeight > maxHeight)
        {
            float temp = minHeight;
            minHeight = maxHeight;
            maxHeight = temp;
        }
    }
}