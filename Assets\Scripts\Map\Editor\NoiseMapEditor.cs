using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(NoiseMap))]
public class NoiseMapEditor : Editor
{
    public override void OnInspectorGUI()
    {
        NoiseMap noiseMap = (NoiseMap)target;
        
        // 绘制默认Inspector
        DrawDefaultInspector();
        
        GUILayout.Space(10);
        
        // 添加按钮区域
        GUILayout.BeginVertical("box");
        GUILayout.Label("地图生成控制", EditorStyles.boldLabel);
        
        GUILayout.BeginHorizontal();
        
        // 生成地图按钮
        if (GUILayout.Button("生成地图", GUILayout.Height(30)))
        {
            noiseMap.GenerateMapManually();
        }
        
        // 随机种子按钮
        if (GUILayout.Button("随机种子", GUILayout.Height(30)))
        {
            noiseMap.RandomizeSeed();
        }
        
        GUILayout.EndHorizontal();
        
        // 显示当前配置信息
        if (noiseMap.noiseConfig != null)
        {
            GUILayout.Space(5);
            GUILayout.Label("当前配置信息:", EditorStyles.boldLabel);
            GUILayout.Label($"种子: {noiseMap.noiseConfig.seed}");
            GUILayout.Label($"地图尺寸: {noiseMap.noiseConfig.mapWidth} x {noiseMap.noiseConfig.mapHeight}");
            GUILayout.Label($"噪声缩放: {noiseMap.noiseConfig.noiseScale:F4}");
            GUILayout.Label($"Octaves: {noiseMap.noiseConfig.octaves}");
            GUILayout.Label($"自动更新: {(noiseMap.noiseConfig.autoUpdate ? "开启" : "关闭")}");
        }
        
        GUILayout.EndVertical();
        
        // 如果配置改变了，重新绘制Inspector
        if (GUI.changed)
        {
            EditorUtility.SetDirty(target);
        }
    }
}
