using UnityEngine;

public static class NoiseAlgorithm
{
    // 生成噪声地图的主要方法
    public static float[,] GenerateNoiseMap(NoiseConfigSO config)
    {
        float[,] noiseMap = new float[config.mapWidth, config.mapHeight];

        // 使用种子初始化随机数生成器
        System.Random prng = new System.Random(config.seed);
        Vector2[] octaveOffsets = new Vector2[config.octaves];

        // 为每个octave生成随机偏移
        for (int i = 0; i < config.octaves; i++)
        {
            float offsetX = prng.Next(-100000, 100000) + config.offsetX;
            float offsetY = prng.Next(-100000, 100000) + config.offsetY;
            octaveOffsets[i] = new Vector2(offsetX, offsetY);
        }

        // 防止除零错误
        if (config.noiseScale <= 0)
        {
            config.noiseScale = 0.0001f;
        }

        float maxNoiseHeight = float.MinValue;
        float minNoiseHeight = float.MaxValue;

        // 计算地图中心点，用于缩放时保持中心不变
        float halfWidth = config.mapWidth / 2f;
        float halfHeight = config.mapHeight / 2f;

        // 生成噪声值
        for (int y = 0; y < config.mapHeight; y++)
        {
            for (int x = 0; x < config.mapWidth; x++)
            {
                float amplitude = 1;
                float frequency = 1;
                float noiseHeight = 0;

                // 叠加多个octave
                for (int i = 0; i < config.octaves; i++)
                {
                    float sampleX = (x - halfWidth) / config.noiseScale * frequency + octaveOffsets[i].x;
                    float sampleY = (y - halfHeight) / config.noiseScale * frequency + octaveOffsets[i].y;

                    // Unity的Perlin噪声返回0-1，我们转换为-1到1
                    float perlinValue = Mathf.PerlinNoise(sampleX, sampleY) * 2 - 1;
                    noiseHeight += perlinValue * amplitude;

                    amplitude *= config.persistence;
                    frequency *= config.lacunarity;
                }

                // 记录最大最小值用于后续归一化
                if (noiseHeight > maxNoiseHeight)
                {
                    maxNoiseHeight = noiseHeight;
                }
                else if (noiseHeight < minNoiseHeight)
                {
                    minNoiseHeight = noiseHeight;
                }

                noiseMap[x, y] = noiseHeight;
            }
        }

        // 归一化噪声值到0-1范围并应用高度曲线
        for (int y = 0; y < config.mapHeight; y++)
        {
            for (int x = 0; x < config.mapWidth; x++)
            {
                // 归一化到0-1
                noiseMap[x, y] = Mathf.InverseLerp(minNoiseHeight, maxNoiseHeight, noiseMap[x, y]);

                // 应用高度曲线重分布
                noiseMap[x, y] = Mathf.Pow(noiseMap[x, y], config.heightCurve);

                // 应用边缘平滑
                if (config.enableEdgeSmoothing)
                {
                    float edgeFactor = CalculateEdgeFactor(x, y, config);
                    noiseMap[x, y] = Mathf.Lerp(noiseMap[x, y], 0f, edgeFactor * config.edgeSmoothingStrength);
                }

                // 应用高度范围限制
                noiseMap[x, y] = Mathf.Lerp(config.minHeight, config.maxHeight, noiseMap[x, y]);
            }
        }

        return noiseMap;
    }

    // 计算边缘平滑因子
    private static float CalculateEdgeFactor(int x, int y, NoiseConfigSO config)
    {
        // 计算到边缘的最小距离（归一化）
        float distanceToEdgeX = Mathf.Min(x, config.mapWidth - 1 - x) / (float)config.mapWidth;
        float distanceToEdgeY = Mathf.Min(y, config.mapHeight - 1 - y) / (float)config.mapHeight;
        float minDistanceToEdge = Mathf.Min(distanceToEdgeX, distanceToEdgeY);

        // 如果距离边缘超过平滑范围，返回0（不平滑）
        if (minDistanceToEdge > config.edgeSmoothingRange)
        {
            return 0f;
        }

        // 在平滑范围内，计算平滑因子（距离边缘越近，平滑因子越大）
        float smoothingFactor = 1f - (minDistanceToEdge / config.edgeSmoothingRange);
        return smoothingFactor;
    }

    // 生成颜色地图用于可视化（可选功能）
    public static Color[] GenerateColorMap(float[,] noiseMap, int width, int height)
    {
        Color[] colorMap = new Color[width * height];

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float noiseValue = noiseMap[x, y];
                Color color = GetHeightColor(noiseValue);
                colorMap[y * width + x] = color;
            }
        }

        return colorMap;
    }

    // 根据高度值获取对应颜色
    private static Color GetHeightColor(float height)
    {
        // 简单的高度-颜色映射
        if (height < 0.3f)
        {
            return Color.Lerp(Color.blue, Color.cyan, height / 0.3f); // 水域
        }
        else if (height < 0.4f)
        {
            return Color.Lerp(Color.cyan, Color.yellow, (height - 0.3f) / 0.1f); // 海滩
        }
        else if (height < 0.6f)
        {
            return Color.Lerp(Color.green, Color.green * 0.8f, (height - 0.4f) / 0.2f); // 草地
        }
        else if (height < 0.8f)
        {
            return Color.Lerp(Color.green * 0.6f, Color.gray, (height - 0.6f) / 0.2f); // 山地
        }
        else
        {
            return Color.Lerp(Color.gray, Color.white, (height - 0.8f) / 0.2f); // 雪山
        }
    }

    // 生成纹理用于显示
    public static Texture2D GenerateTexture(float[,] noiseMap, int width, int height)
    {
        Color[] colorMap = GenerateColorMap(noiseMap, width, height);
        return GenerateTextureFromColorMap(colorMap, width, height);
    }

    // 从颜色数组生成纹理
    public static Texture2D GenerateTextureFromColorMap(Color[] colorMap, int width, int height)
    {
        Texture2D texture = new Texture2D(width, height);
        texture.filterMode = FilterMode.Point;
        texture.wrapMode = TextureWrapMode.Clamp;
        texture.SetPixels(colorMap);
        texture.Apply();
        return texture;
    }
}